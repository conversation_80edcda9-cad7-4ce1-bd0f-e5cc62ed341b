package org.jeecg.modules.corp.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class RandDayNumUtil {

    // 24小时真实点击概率分布权重（基于真实用户行为模式）
    private static final double[] HOUR_WEIGHTS = {
        0.5,    // 0点 - 深夜
        0.01,   // 1点 - 时段1-6只有0.01%概率，设置极低权重
        0.01,   // 2点
        0.01,   // 3点
        0.01,   // 4点
        0.01,   // 5点
        0.01,   // 6点
        3.0,    // 7点 - 早高峰开始
        4.5,    // 8点
        5.0,    // 9点 - 上班时间
        5.5,    // 10点
        6.0,    // 11点
        4.0,    // 12点 - 午餐时间
        3.5,    // 13点
        5.0,    // 14点 - 下午工作时间
        5.5,    // 15点
        4.5,    // 16点
        4.0,    // 17点
        3.5,    // 18点 - 下班时间
        3.0,    // 19点 - 晚餐时间
        4.0,    // 20点 - 晚间活跃时间
        4.5,    // 21点
        3.0,    // 22点
        1.5     // 23点
    };

    // 预计算累积权重，提升性能
    private static final double[] CUMULATIVE_WEIGHTS;
    private static final double TOTAL_WEIGHT;

    static {
        CUMULATIVE_WEIGHTS = new double[24];
        double sum = 0;
        for (int i = 0; i < 24; i++) {
            sum += HOUR_WEIGHTS[i];
            CUMULATIVE_WEIGHTS[i] = sum;
        }
        TOTAL_WEIGHT = sum;
    }

    public static Map<String, LocalDateTime> generateTimeMap(LocalDate date, int startNum, int endNum) {
        return generateTimeMap(date, (double) startNum, (double) endNum);
    }

    public static Map<String, LocalDateTime> generateTimeMap(LocalDate date, Double startNum, Double endNum) {
        ThreadLocalRandom random = ThreadLocalRandom.current();

        // 生成总数量
        double rand = startNum + random.nextDouble() * (endNum - startNum);
        int totalCount = (int) Math.round(rand);

        if (totalCount <= 0) {
            return new LinkedHashMap<>();
        }

        // 使用加权随机分配到各个小时
        int[] hourCounts = distributeCountsByWeight(totalCount, date);

        // 生成具体时间点
        List<LocalDateTime> allTimes = generateTimesFromHourCounts(date, hourCounts);

        // 排序并构建结果Map
        allTimes.sort(null);

        Map<String, LocalDateTime> resultMap = new LinkedHashMap<>(allTimes.size());
        for (int i = 0; i < allTimes.size(); i++) {
            resultMap.put("key_" + (i + 1), allTimes.get(i));
        }

        return resultMap;
    }

    /**
     * 根据权重分布将总数量分配到各个小时
     */
    private static int[] distributeCountsByWeight(int totalCount, LocalDate date) {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        int[] hourCounts = new int[24];

        // 添加日期种子，确保每天的随机性不同
        long dateSeed = date.toEpochDay();
        Random dateRandom = new Random(dateSeed + System.currentTimeMillis() % 1000);

        // 为每小时的权重添加随机波动（±20%），模拟真实世界的不确定性
        double[] adjustedWeights = new double[24];
        double adjustedTotal = 0;

        for (int i = 0; i < 24; i++) {
            // 对时段1-6（索引1-6）保持极低概率，不添加波动
            if (i >= 1 && i <= 6) {
                adjustedWeights[i] = HOUR_WEIGHTS[i]; // 保持极低权重，不添加波动
            } else {
                adjustedWeights[i] = HOUR_WEIGHTS[i] * (0.8 + dateRandom.nextDouble() * 0.4); // ±20%波动
            }
            adjustedTotal += adjustedWeights[i];
        }

        // 按调整后的权重分配数量
        for (int i = 0; i < totalCount; i++) {
            double randomValue = random.nextDouble() * adjustedTotal;
            double cumulative = 0;

            for (int hour = 0; hour < 24; hour++) {
                cumulative += adjustedWeights[hour];
                if (randomValue <= cumulative) {
                    hourCounts[hour]++;
                    break;
                }
            }
        }

        return hourCounts;
    }

    /**
     * 根据每小时的数量生成具体的时间点
     */
    private static List<LocalDateTime> generateTimesFromHourCounts(LocalDate date, int[] hourCounts) {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        List<LocalDateTime> result = new ArrayList<>();

        for (int hour = 0; hour < 24; hour++) {
            int count = hourCounts[hour];
            if (count > 0) {
                // 为了避免时间重复，使用Set来存储该小时内的分钟秒组合
                Set<Integer> usedMinuteSeconds = new HashSet<>();

                for (int i = 0; i < count; i++) {
                    int minuteSecond;
                    int attempts = 0;

                    // 尝试生成不重复的分钟秒组合，最多尝试100次
                    do {
                        int minute = random.nextInt(60);
                        int second = random.nextInt(60);
                        minuteSecond = minute * 60 + second;
                        attempts++;
                    } while (usedMinuteSeconds.contains(minuteSecond) && attempts < 100);

                    usedMinuteSeconds.add(minuteSecond);
                    int minute = minuteSecond / 60;
                    int second = minuteSecond % 60;

                    result.add(LocalDateTime.of(date, LocalTime.of(hour, minute, second)));
                }
            }
        }

        return result;
    }

    /**
     * 随机从 resultMap 中按 randRateMin ~ randRateMax 比例选取部分数据
     * @param resultMap 原始 Map<String, LocalDateTime>
     * @param randRateMin 比率下限（百分比）
     * @param randRateMax 比率上限（百分比）
     * @return 随机选出的 Map<String, LocalDateTime>
     */
    public static Map<String, LocalDateTime> pickByRandomRate(Map<String, LocalDateTime> resultMap, Double randRateMin, Double randRateMax) {
        if (resultMap == null || resultMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // 使用ThreadLocalRandom提升性能
        ThreadLocalRandom random = ThreadLocalRandom.current();
        double randRate = randRateMin + random.nextDouble() * (randRateMax - randRateMin);

        int totalSize = resultMap.size();
        int pickSize = (int) Math.round(totalSize * randRate / 100.0);

        if (pickSize <= 0) {
            return Collections.emptyMap();
        }

        List<Map.Entry<String, LocalDateTime>> entries = new ArrayList<>(resultMap.entrySet());
        Collections.shuffle(entries, new Random(random.nextLong())); // 打乱顺序

        Map<String, LocalDateTime> ledgerTimeMap = new LinkedHashMap<>(pickSize);
        for (int i = 0; i < pickSize && i < entries.size(); i++) {
            Map.Entry<String, LocalDateTime> entry = entries.get(i);
            ledgerTimeMap.put(entry.getKey(), entry.getValue());
        }

        return ledgerTimeMap;
    }

    /**
     * 重载方法，支持int类型参数，向下兼容
     */
    public static Map<String, LocalDateTime> pickByRandomRate(Map<String, LocalDateTime> resultMap, int randRateMin, int randRateMax) {
        return pickByRandomRate(resultMap, (double) randRateMin, (double) randRateMax);
    }

    /**
     * 分析时段分布情况，用于验证算法效果
     * @param timeMap 时间映射
     * @return 各时段的统计信息
     */
    public static Map<String, Object> analyzeTimeDistribution(Map<String, LocalDateTime> timeMap) {
        if (timeMap == null || timeMap.isEmpty()) {
            return Collections.emptyMap();
        }

        int[] hourCounts = new int[24];
        int totalCount = timeMap.size();

        // 统计各小时的数量
        for (LocalDateTime time : timeMap.values()) {
            hourCounts[time.getHour()]++;
        }

        Map<String, Object> analysis = new LinkedHashMap<>();
        analysis.put("总数量", totalCount);

        // 统计时段1-6的数量和比例（1点到6点）
        int earlyHourCount = 0;
        for (int i = 1; i <= 6; i++) {
            earlyHourCount += hourCounts[i];
        }
        double earlyHourPercentage = (double) earlyHourCount / totalCount * 100;
        analysis.put("时段1-6数量", earlyHourCount);
        analysis.put("时段1-6比例", String.format("%.4f%%", earlyHourPercentage));

        // 各小时详细统计
        Map<String, String> hourlyStats = new LinkedHashMap<>();
        for (int i = 0; i < 24; i++) {
            double percentage = (double) hourCounts[i] / totalCount * 100;
            hourlyStats.put(i + "点", String.format("%d次 (%.2f%%)", hourCounts[i], percentage));
        }
        analysis.put("各小时统计", hourlyStats);

        return analysis;
    }

    /**
     * 测试方法 - 基本功能测试
     */
    public static void testBasicFunction() {
        System.out.println("=== 基本功能测试 ===");
        LocalDate targetDate = LocalDate.of(2025, 1, 15);
        Map<String, LocalDateTime> resultMap = generateTimeMap(targetDate, 1000, 2000);

        System.out.println("生成的时间数量: " + resultMap.size());

        // 分析时段分布
        Map<String, Object> analysis = analyzeTimeDistribution(resultMap);
        System.out.println("分布分析: " + analysis);

        // 显示前10个时间点
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        System.out.println("\n前10个时间点:");
        resultMap.entrySet().stream()
                .limit(10)
                .forEach(entry -> System.out.println(entry.getKey() + " -> " + entry.getValue().format(formatter)));
    }

    /**
     * 测试方法 - 多日期对比测试
     */
    public static void testMultipleDates() {
        System.out.println("\n=== 多日期对比测试 ===");
        LocalDate[] dates = {
            LocalDate.of(2025, 1, 15),
            LocalDate.of(2025, 1, 16),
            LocalDate.of(2025, 1, 17)
        };

        for (LocalDate date : dates) {
            Map<String, LocalDateTime> resultMap = generateTimeMap(date, 1000, 1000);
            Map<String, Object> analysis = analyzeTimeDistribution(resultMap);

            System.out.println("\n日期: " + date);
            System.out.println("时段1-6比例: " + analysis.get("时段1-6比例"));

            // 显示高峰时段统计
            @SuppressWarnings("unchecked")
            Map<String, String> hourlyStats = (Map<String, String>) analysis.get("各小时统计");
            System.out.println("9点统计: " + hourlyStats.get("9点"));
            System.out.println("14点统计: " + hourlyStats.get("14点"));
            System.out.println("21点统计: " + hourlyStats.get("21点"));
        }
    }

    /**
     * 测试方法 - 性能测试
     */
    public static void testPerformance() {
        System.out.println("\n=== 性能测试 ===");
        LocalDate targetDate = LocalDate.of(2025, 1, 15);

        // 测试大数据量生成
        long startTime = System.currentTimeMillis();
        Map<String, LocalDateTime> largeResultMap = generateTimeMap(targetDate, 10000, 20000);
        long endTime = System.currentTimeMillis();

        System.out.println("大数据量生成耗时: " + (endTime - startTime) + "ms");
        System.out.println("生成数量: " + largeResultMap.size());

        // 测试随机选取性能
        startTime = System.currentTimeMillis();
        Map<String, LocalDateTime> pickedMap = pickByRandomRate(largeResultMap, 10.0, 30.0);
        endTime = System.currentTimeMillis();

        System.out.println("随机选取耗时: " + (endTime - startTime) + "ms");
        System.out.println("选取数量: " + pickedMap.size());
    }

    /**
     * 主测试方法
     */
    public static void main(String[] args) {
        testBasicFunction();
        testMultipleDates();
        testPerformance();
    }

}
