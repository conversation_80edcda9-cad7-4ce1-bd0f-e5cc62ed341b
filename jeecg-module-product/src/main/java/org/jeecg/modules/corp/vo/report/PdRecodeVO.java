package org.jeecg.modules.corp.vo.report;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
@Accessors(chain = true)
public class PdRecodeVO implements Serializable {

    @ApiModelProperty(value = "访问量 PV")
    private Integer clickPv;

    @ApiModelProperty(value = "独立访客 UV")
    private Integer  clickNum;

    @ApiModelProperty(value = "跳出率(%)")
    private BigDecimal bounceRate;

    @ApiModelProperty(value = "转化率(%)")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "表单提交数")
    private Integer formSubmissions;

    @ApiModelProperty(value = "点击率(%)")
    private BigDecimal ctr;

    @ApiModelProperty(value = "平均停留时长(秒)")
    private BigDecimal avgStayTime;

    @ApiModelProperty(value = "返回率(%)")
    private BigDecimal returnRate;

    @ApiModelProperty(value = "内容完成率(%)")
    private BigDecimal completionRate;

}
