package org.jeecg.modules.corp.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.corp.dto.ClickDto;
import org.jeecg.modules.corp.entity.ClickReportHourly;
import org.jeecg.modules.corp.mapper.ClickReportHourlyMapper;
import org.jeecg.modules.corp.service.IClickReportHourlyService;
import org.jeecg.modules.corp.vo.report.ClickReportHourlyVO;
import org.jeecg.modules.corp.vo.report.PdRecodeVO;
import org.jeecg.modules.corp.vo.report.ReportCityVO;
import org.jeecg.modules.corp.vo.report.ReportCurveVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: 按小时统计点击报表Service实现
 * @Author: jeecg-boot
 * @Date: 2024-05-16
 */
@Service
public class ClickReportHourlyServiceImpl extends ServiceImpl<ClickReportHourlyMapper, ClickReportHourly> implements IClickReportHourlyService {

    @Override
    public List<Map<String, Object>> getClickStatsByDateRange(Map<String, Object> params) {
        return baseMapper.getClickStatsByDateRange(params);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickReportHourly add(ClickReportHourly dto) {
        save(dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickReportHourly edit(ClickReportHourly dto) {
        updateById(dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id) {
        baseMapper.deleteById(id);
    }

    @Override
    public IPage<ClickReportHourly> findPage(IPage<ClickReportHourly> page, ClickReportHourly dto) {
        return baseMapper.selectPage(page, null);
    }

    @Override
    public Integer getTodayClickCount(Integer tenantId) {
        return baseMapper.getTodayClickCount(tenantId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addClickByTenantId(Integer tenantId) {
        baseMapper.addClickByTenantId(tenantId);
    }

    @Override
    public Integer getTodayReservationCount(Integer tenantId) {
        return baseMapper.getTodayReservationCount(tenantId);
    }

    @Override
    public ClickReportHourlyVO getScreenDate(ClickDto dto) {
        ClickReportHourlyVO result = new ClickReportHourlyVO();
        //1. 获取时段曲线图,根据 dto 中的日期与租户 id 参数,查询基础数据click_report_hourly与预约表们按照创建时间与 dto 中的日期相等,需要将去除创建时间的时分秒,总数即可:pd_added,pd_car_info,pd_casualty_info,完成 xml 语句编写,查询出List<ReportCurveVO> curveList
        List<ReportCurveVO> curveList = getCurveList(dto);

        //2. 获取指标类数据,根据 dto 中的日期与租户 id 参数,查询PdRecodeVO基础数据pd_link_recode,pv和 uv 的汇总总数取click_report_hourly
        PdRecodeVO recodeVo = getRecodeVo(dto);

        //3.  获取城市数据,根据 dto 中的日期与租户 id 参数,查询基础数据预约表:pd_added,pd_car_info,pd_casualty_info中的城市汇总数据,完成 xml 语句编写
        List<ReportCityVO> cityRateList = getCityRateList(dto);

        result.setCurveList(curveList);
        result.setRecodeVo(recodeVo);
        result.setCityRateList(cityRateList);
        return result;
    }

    /**
     * 获取时段曲线图数据
     * @param dto 查询参数
     * @return 时段曲线图数据列表
     */
    private List<ReportCurveVO> getCurveList(ClickDto dto) {
        List<Map<String, Object>> dataList = baseMapper.getCurveData(dto.getTenantId(), dto.getOrderDate());
        List<ReportCurveVO> curveList = new ArrayList<>();

        for (Map<String, Object> data : dataList) {
            ReportCurveVO curveVO = new ReportCurveVO();
            curveVO.setTimeFrame(String.valueOf(data.get("timeFrame")));
            curveVO.setClickPv(String.valueOf(data.get("clickPv")));
            curveVO.setClickNum(String.valueOf(data.get("clickNum")));
            curveVO.setCarInsuranceReservationNum(String.valueOf(data.get("carInsuranceReservationNum")));
            curveVO.setTravelInsuranceReservationNum(String.valueOf(data.get("travelInsuranceReservationNum")));
            curveVO.setAdditionalServiceReservationNum(String.valueOf(data.get("additionalServiceReservationNum")));
            curveVO.setReservationNum(String.valueOf(data.get("reservationNum")));

            // 计算转化率 = 预约总数/点击数
            Integer clickNum = Integer.valueOf(String.valueOf(data.get("clickNum")));
            Integer reservationNum = Integer.valueOf(String.valueOf(data.get("reservationNum")));
            if (clickNum > 0) {
                BigDecimal rate = new BigDecimal(reservationNum)
                    .divide(new BigDecimal(clickNum), 4, RoundingMode.HALF_UP);
                curveVO.setReservationRate(rate);
            } else {
                curveVO.setReservationRate(BigDecimal.ZERO);
            }

            curveList.add(curveVO);
        }

        return curveList;
    }

    /**
     * 获取城市汇总数据
     * @param dto 查询参数
     * @return 城市汇总数据列表
     */
    private List<ReportCityVO> getCityRateList(ClickDto dto) {
        List<Map<String, Object>> dataList = baseMapper.getCityData(dto.getTenantId(), dto.getOrderDate());
        List<ReportCityVO> cityRateList = new ArrayList<>();

        // 计算总数用于计算占比
        int totalCount = 0;
        for (Map<String, Object> data : dataList) {
            Integer count = Integer.valueOf(String.valueOf(data.get("totalCount")));
            totalCount += count;
        }

        // 构建城市数据并计算占比
        for (Map<String, Object> data : dataList) {
            ReportCityVO cityVO = new ReportCityVO();
            cityVO.setCity(String.valueOf(data.get("city")));

            Integer count = Integer.valueOf(String.valueOf(data.get("totalCount")));
            if (totalCount > 0) {
                BigDecimal rate = new BigDecimal(count)
                    .divide(new BigDecimal(totalCount), 4, RoundingMode.HALF_UP);
                cityVO.setCityRate(rate);
            } else {
                cityVO.setCityRate(BigDecimal.ZERO);
            }

            cityRateList.add(cityVO);
        }

        return cityRateList;
    }

    /**
     * 获取指标类数据
     * @param dto 查询参数
     * @return 指标数据
     */
    private PdRecodeVO getRecodeVo(ClickDto dto) {
        Map<String, Object> data = baseMapper.getRecodeData(dto.getTenantId(), dto.getOrderDate());
        PdRecodeVO recodeVO = new PdRecodeVO();

        // 设置基础数据
        recodeVO.setClickPv(Integer.valueOf(String.valueOf(data.get("clickPv"))));
        recodeVO.setClickNum(Integer.valueOf(String.valueOf(data.get("clickNum"))));
        recodeVO.setFormSubmissions(Integer.valueOf(String.valueOf(data.get("formSubmissions"))));

        // 设置百分比数据，需要转换为BigDecimal
        recodeVO.setBounceRate(new BigDecimal(String.valueOf(data.get("bounceRate"))));
        recodeVO.setConversionRate(new BigDecimal(String.valueOf(data.get("conversionRate"))));
        recodeVO.setCtr(new BigDecimal(String.valueOf(data.get("ctr"))));
        recodeVO.setReturnRate(new BigDecimal(String.valueOf(data.get("returnRate"))));
        recodeVO.setCompletionRate(new BigDecimal(String.valueOf(data.get("completionRate"))));

        // 设置平均停留时长
        recodeVO.setAvgStayTime(new BigDecimal(String.valueOf(data.get("avgStayTime"))));

        return recodeVO;
    }
}
