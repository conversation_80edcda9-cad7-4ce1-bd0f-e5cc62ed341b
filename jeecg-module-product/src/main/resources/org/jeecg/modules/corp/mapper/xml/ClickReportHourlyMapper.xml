<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.corp.mapper.ClickReportHourlyMapper">

    <!-- 根据日期范围获取点击统计数据 -->
    <select id="getClickStatsByDateRange" resultType="java.util.HashMap">
        SELECT
            stat_date,
            SUM(click_num) as total_uv,
            SUM(click_pv) as total_pv
        FROM
            click_report_hourly
        WHERE
            1=1
            <if test="params.startDate != null">
                AND stat_date &gt;= #{params.startDate}
            </if>
            <if test="params.endDate != null">
                AND stat_date &lt;= #{params.endDate}
            </if>
            <if test="params.configType != null">
                AND config_type = #{params.configType}
            </if>
            <if test="params.tenantId != null">
                AND tenant_id = #{params.tenantId}
            </if>
        GROUP BY
            stat_date
        ORDER BY
            stat_date ASC
    </select>

    <!-- 获取今日点击数统计 -->
    <select id="getTodayClickCount" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(click_num), 0) as total_clicks
        FROM
            click_report_hourly
        WHERE
            stat_date = CURDATE()
            AND config_type = 0
            <if test="tenantId != null and tenantId != 0">
                AND tenant_id = #{tenantId}
            </if>
    </select>

    <!-- 获取今日预约总数统计 -->
    <select id="getTodayReservationCount" resultType="java.lang.Integer">
        SELECT
            IFNULL(
                (SELECT COUNT(*) FROM pd_car_info WHERE DATE(create_time) = CURDATE()
                 <if test="tenantId != null and tenantId != 0">AND tenant_id = #{tenantId}</if>) +
                (SELECT COUNT(*) FROM pd_added WHERE DATE(create_time) = CURDATE()
                 <if test="tenantId != null and tenantId != 0">AND tenant_id = #{tenantId}</if>) +
                (SELECT COUNT(*) FROM pd_casualty_info WHERE DATE(create_time) = CURDATE()
                 <if test="tenantId != null and tenantId != 0">AND tenant_id = #{tenantId}</if>)
            , 0) as total_reservations
    </select>

    <!-- 根据租户ID添加点击数 -->
    <insert id="addClickByTenantId">
        INSERT INTO click_report_hourly (
            id,
            click_num,
            stat_date,
            hour,
            tenant_id,
            click_pv,
            config_type
        ) VALUES (
            REPLACE(UUID(), '-', ''),
            1,
            CURDATE(),
            HOUR(NOW()),
            #{tenantId},
            1,
            0
        )
    </insert>

    <!-- 获取时段曲线图数据 -->
    <select id="getCurveData" resultType="java.util.HashMap">
        SELECT
            h.hour_value as hour,
            CONCAT(LPAD(h.hour_value, 2, '0'), ':00-', LPAD(h.hour_value + 1, 2, '0'), ':00') as timeFrame,
            IFNULL(SUM(crh.click_pv), 0) as clickPv,
            IFNULL(SUM(crh.click_num), 0) as clickNum,
            IFNULL(MAX(car_count.count), 0) as carInsuranceReservationNum,
            IFNULL(MAX(casualty_count.count), 0) as travelInsuranceReservationNum,
            IFNULL(MAX(added_count.count), 0) as additionalServiceReservationNum,
            (IFNULL(MAX(car_count.count), 0) + IFNULL(MAX(casualty_count.count), 0) + IFNULL(MAX(added_count.count), 0)) as reservationNum
        FROM
            (SELECT 0 as hour_value UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5
             UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10 UNION SELECT 11
             UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION SELECT 15 UNION SELECT 16 UNION SELECT 17
             UNION SELECT 18 UNION SELECT 19 UNION SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23) h
        LEFT JOIN click_report_hourly crh ON h.hour_value = crh.hour
            AND DATE(crh.stat_date) = #{orderDate}
            <if test="tenantId != null and tenantId != ''">
                AND crh.tenant_id = #{tenantId}
            </if>
        LEFT JOIN (
            SELECT HOUR(create_time) as hour, COUNT(*) as count
            FROM pd_car_info
            WHERE DATE(create_time) = #{orderDate}
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
            GROUP BY HOUR(create_time)
        ) car_count ON h.hour_value = car_count.hour
        LEFT JOIN (
            SELECT HOUR(create_time) as hour, COUNT(*) as count
            FROM pd_casualty_info
            WHERE DATE(create_time) = #{orderDate}
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
            GROUP BY HOUR(create_time)
        ) casualty_count ON h.hour_value = casualty_count.hour
        LEFT JOIN (
            SELECT HOUR(create_time) as hour, COUNT(*) as count
            FROM pd_added
            WHERE DATE(create_time) = #{orderDate}
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
            GROUP BY HOUR(create_time)
        ) added_count ON h.hour_value = added_count.hour
        GROUP BY h.hour_value
        ORDER BY h.hour_value
    </select>

    <!-- 获取城市汇总数据 -->
    <select id="getCityData" resultType="java.util.HashMap">
        SELECT
            IFNULL(er.name, '未知城市') as city,
            city_stats.total_count as totalCount,
            city_stats.car_count as carCount,
            city_stats.casualty_count as casualtyCount,
            city_stats.added_count as addedCount
        FROM (
            SELECT
                city_code,
                SUM(total_count) as total_count,
                SUM(car_count) as car_count,
                SUM(casualty_count) as casualty_count,
                SUM(added_count) as added_count
            FROM (
                -- 车险预约数据
                SELECT
                    city_code,
                    COUNT(*) as total_count,
                    COUNT(*) as car_count,
                    0 as casualty_count,
                    0 as added_count
                FROM pd_car_info
                WHERE DATE(create_time) = #{orderDate}
                <if test="tenantId != null and tenantId != ''">
                    AND tenant_id = #{tenantId}
                </if>
                AND city_code IS NOT NULL AND city_code != ''
                GROUP BY city_code

                UNION ALL

                -- 财险预约数据
                SELECT
                    city_code,
                    COUNT(*) as total_count,
                    0 as car_count,
                    COUNT(*) as casualty_count,
                    0 as added_count
                FROM pd_casualty_info
                WHERE DATE(create_time) = #{orderDate}
                <if test="tenantId != null and tenantId != ''">
                    AND tenant_id = #{tenantId}
                </if>
                AND city_code IS NOT NULL AND city_code != ''
                GROUP BY city_code

                UNION ALL

                -- 增值服务预约数据
                SELECT
                    city_code,
                    COUNT(*) as total_count,
                    0 as car_count,
                    0 as casualty_count,
                    COUNT(*) as added_count
                FROM pd_added
                WHERE DATE(create_time) = #{orderDate}
                <if test="tenantId != null and tenantId != ''">
                    AND tenant_id = #{tenantId}
                </if>
                AND city_code IS NOT NULL AND city_code != ''
                GROUP BY city_code
            ) combined_data
            GROUP BY city_code
        ) city_stats
        LEFT JOIN ea_region er ON city_stats.city_code = er.code
        WHERE city_stats.total_count > 0
        ORDER BY city_stats.total_count DESC
    </select>

    <!-- 获取指标类数据 -->
    <select id="getRecodeData" resultType="java.util.HashMap">
        SELECT
            -- 访问量 PV (从click_report_hourly表汇总)
            IFNULL(SUM(crh.click_pv), 0) as clickPv,
            -- 独立访客 UV (从click_report_hourly表汇总)
            IFNULL(SUM(crh.click_num), 0) as clickNum,
            -- 表单提交数 (预约总数)
            IFNULL(MAX(reservation_stats.total_reservations), 0) as formSubmissions,
            -- 从pd_link_recode表获取其他指标的平均值
            IFNULL(AVG(plr.bounce_rate), 0) as bounceRate,
            IFNULL(AVG(plr.conversion_rate), 0) as conversionRate,
            IFNULL(AVG(plr.ctr), 0) as ctr,
            IFNULL(AVG(plr.avg_stay_time), 0) as avgStayTime,
            IFNULL(AVG(plr.return_rate), 0) as returnRate,
            IFNULL(AVG(plr.completion_rate), 0) as completionRate
        FROM
            (SELECT 1 as dummy) d
        LEFT JOIN click_report_hourly crh ON DATE(crh.stat_date) = #{orderDate}
            <if test="tenantId != null and tenantId != ''">
                AND crh.tenant_id = #{tenantId}
            </if>
        LEFT JOIN pd_link_recode plr ON DATE(plr.link_date) = #{orderDate}
            <if test="tenantId != null and tenantId != ''">
                AND plr.tenant_id = #{tenantId}
            </if>
        LEFT JOIN (
            SELECT
                (
                    (SELECT COUNT(*) FROM pd_car_info WHERE DATE(create_time) = #{orderDate}
                     <if test="tenantId != null and tenantId != ''">AND tenant_id = #{tenantId}</if>) +
                    (SELECT COUNT(*) FROM pd_casualty_info WHERE DATE(create_time) = #{orderDate}
                     <if test="tenantId != null and tenantId != ''">AND tenant_id = #{tenantId}</if>) +
                    (SELECT COUNT(*) FROM pd_added WHERE DATE(create_time) = #{orderDate}
                     <if test="tenantId != null and tenantId != ''">AND tenant_id = #{tenantId}</if>)
                ) as total_reservations
        ) reservation_stats ON 1=1
    </select>

</mapper>