-- 禁用外键检查（预防某些 DDL 报错）
SET FOREIGN_KEY_CHECKS = 0;

-- 删除已有过程（防止重复定义）
DROP PROCEDURE IF EXISTS `schema_change`;
DELIMITER ;;

CREATE PROCEDURE schema_change()
BEGIN
    -- 为 pd_car_info 表添加 city_code 字段
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS
        WHERE table_schema = DATABASE()
          AND table_name = 'pd_car_info'
          AND column_name = 'city_code'
    ) THEN
        ALTER TABLE `pd_car_info`
        ADD COLUMN `city_code` varchar(20) NULL DEFAULT NULL COMMENT '城市编码' AFTER `city`;
    END IF;

    -- 为 pd_casualty_info 表添加 city_code 字段
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS
        WHERE table_schema = DATABASE()
          AND table_name = 'pd_casualty_info'
          AND column_name = 'city_code'
    ) THEN
        ALTER TABLE `pd_casualty_info`
        ADD COLUMN `city_code` varchar(20) NULL DEFAULT NULL COMMENT '城市编码' AFTER `tenant_id`;
    END IF;

    -- 为 pd_added 表添加 city_code 字段
    IF NOT EXISTS (
        SELECT * FROM information_schema.COLUMNS
        WHERE table_schema = DATABASE()
          AND table_name = 'pd_added'
          AND column_name = 'city_code'
    ) THEN
        ALTER TABLE `pd_added`
        ADD COLUMN `city_code` varchar(20) NULL DEFAULT NULL COMMENT '城市编码' AFTER `tenant_id`;
    END IF;
END;;
DELIMITER ;

-- 执行存储过程
CALL schema_change();

-- 删除过程
DROP PROCEDURE IF EXISTS `schema_change`;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
